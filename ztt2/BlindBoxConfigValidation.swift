//
//  BlindBoxConfigValidation.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import Foundation
import CoreData

/**
 * 盲盒配置功能验证类
 * 用于手动验证盲盒配置功能的正确性
 */
class BlindBoxConfigValidation {
    
    static func validateBlindBoxConfig() {
        print("🧪 开始验证盲盒配置功能...")
        
        // 创建测试环境
        let persistenceController = PersistenceController(inMemory: true)
        let testContext = persistenceController.container.viewContext
        let dataManager = DataManager.shared
        
        // 创建测试用户和成员
        let user = User(context: testContext)
        user.id = UUID()
        user.nickname = "测试用户"
        user.createdAt = Date()
        
        let member = Member(context: testContext)
        member.id = UUID()
        member.name = "测试成员"
        member.role = "son"
        member.currentPoints = 100
        member.totalPoints = 100
        member.createdAt = Date()
        member.user = user
        
        do {
            try testContext.save()
            print("✅ 测试环境创建成功")
        } catch {
            print("❌ 测试环境创建失败: \(error)")
            return
        }
        
        // 测试1: 保存盲盒配置
        print("\n📝 测试1: 保存盲盒配置")
        let boxCount = 5
        let costPerPlay = 20
        let boxPrizes = ["小贴纸", "铅笔", "橡皮", "尺子", "小玩具"]
        
        let savedConfig = dataManager.saveBlindBoxConfig(
            for: member,
            boxCount: boxCount,
            costPerPlay: costPerPlay,
            boxPrizes: boxPrizes
        )
        
        if let config = savedConfig {
            print("✅ 盲盒配置保存成功")
            print("   - 盲盒数量: \(config.itemCount)")
            print("   - 消耗积分: \(config.costPerPlay)")
            print("   - 工具类型: \(config.toolType ?? "未知")")
            print("   - 奖品数量: \(config.allItems.count)")
        } else {
            print("❌ 盲盒配置保存失败")
        }
        
        // 测试2: 加载盲盒配置
        print("\n📥 测试2: 加载盲盒配置")
        let loadedConfig = dataManager.getBlindBoxConfig(for: member)
        
        if let config = loadedConfig {
            print("✅ 盲盒配置加载成功")
            print("   - 盲盒数量: \(config.itemCount)")
            print("   - 消耗积分: \(config.costPerPlay)")
            
            let items = config.allItems
            print("   - 奖品列表:")
            for item in items {
                print("     [\(item.itemIndex)] \(item.prizeName ?? "未知")")
            }
        } else {
            print("❌ 盲盒配置加载失败")
        }
        
        // 测试3: 更新盲盒配置
        print("\n🔄 测试3: 更新盲盒配置")
        let updatedBoxCount = 3
        let updatedCostPerPlay = 15
        let updatedBoxPrizes = ["新奖品1", "新奖品2", "新奖品3"]
        
        let updatedConfig = dataManager.saveBlindBoxConfig(
            for: member,
            boxCount: updatedBoxCount,
            costPerPlay: updatedCostPerPlay,
            boxPrizes: updatedBoxPrizes
        )
        
        if let config = updatedConfig {
            print("✅ 盲盒配置更新成功")
            print("   - 新盲盒数量: \(config.itemCount)")
            print("   - 新消耗积分: \(config.costPerPlay)")
            
            // 验证只有一个配置存在
            let allConfigs = member.allLotteryConfigs.filter { $0.lotteryToolType == .blindbox }
            print("   - 配置总数: \(allConfigs.count) (应该为1)")
        } else {
            print("❌ 盲盒配置更新失败")
        }
        
        // 测试4: 数据验证
        print("\n🔍 测试4: 数据验证")
        
        // 测试无效数据
        let invalidConfigs = [
            (0, 10, [""], "盲盒数量为0"),
            (25, 10, Array(repeating: "奖品", count: 25), "盲盒数量超出最大值"),
            (5, -10, Array(repeating: "奖品", count: 5), "负积分"),
            (5, 10, ["奖品1", "奖品2"], "奖品数量不匹配"),
            (3, 10, ["奖品1", "", "奖品3"], "包含空奖品名称")
        ]
        
        for (boxCount, costPerPlay, prizes, description) in invalidConfigs {
            let result = dataManager.saveBlindBoxConfig(
                for: member,
                boxCount: boxCount,
                costPerPlay: costPerPlay,
                boxPrizes: prizes
            )
            
            if result == nil {
                print("✅ \(description) - 验证通过（正确拒绝）")
            } else {
                print("❌ \(description) - 验证失败（应该拒绝但接受了）")
            }
        }
        
        // 测试5: 边界值
        print("\n🎯 测试5: 边界值测试")
        let toolType = LotteryConfig.ToolType.blindbox
        
        // 最小值
        let minConfig = dataManager.saveBlindBoxConfig(
            for: member,
            boxCount: toolType.minItemCount,
            costPerPlay: 0,
            boxPrizes: Array(repeating: "奖品", count: toolType.minItemCount)
        )
        
        if minConfig != nil {
            print("✅ 最小边界值测试通过")
        } else {
            print("❌ 最小边界值测试失败")
        }
        
        // 最大值
        let maxConfig = dataManager.saveBlindBoxConfig(
            for: member,
            boxCount: toolType.maxItemCount,
            costPerPlay: 999,
            boxPrizes: Array(repeating: "奖品", count: toolType.maxItemCount)
        )
        
        if maxConfig != nil {
            print("✅ 最大边界值测试通过")
        } else {
            print("❌ 最大边界值测试失败")
        }
        
        print("\n🎉 盲盒配置功能验证完成！")
    }
}
